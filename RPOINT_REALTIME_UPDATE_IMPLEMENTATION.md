# Real-time R-Point Update Implementation

## ✅ Đ<PERSON>n <PERSON>

### **Y<PERSON><PERSON> cầu**:
Implement real-time R-Point update với SSE event `update_rpoint`:
```json
{
  "event": "update_rpoint",
  "data": {
    "rPointCost": 1396,
    "updatedBalance": "999599026", 
    "timestamp": 1749626848053
  },
  "timestamp": 1749626848053
}
```

## 🔧 Implementation Details

### **1. SSE Service Enhancement**

#### **ChatSSEService.ts - New Event Handler**:
```typescript
// Added callback interface
export interface SSECallbacks {
  // ... existing callbacks
  onUpdateRPoint?: (rPointCost: number, updatedBalance: string, timestamp: number) => void;
}

// Added event handler
private handleUpdateRPointEvent(event: MessageEvent): void {
  try {
    const eventData = JSON.parse(event.data);
    const rPointData = eventData.data;
    const rPointCost = rPointData?.rPointCost || 0;
    const updatedBalance = rPointData?.updatedBalance || '0';
    const timestamp = rPointData?.timestamp || Date.now();

    console.log('[SSE] 🪙 RPoint update:', { rPointCost, updatedBalance, timestamp });
    this.callbacks.onUpdateRPoint?.(rPointCost, updatedBalance, timestamp);
  } catch (error) {
    this.callbacks.onError?.(error as Error);
  }
}

// Added routing in handleSSEEvent
case 'update_rpoint':
  this.handleUpdateRPointEvent(event);
  break;
```

### **2. Chat Stream Integration**

#### **useChatStream.ts - Config & Handler**:
```typescript
// Added to config interface
export interface UseChatStreamConfig {
  // ... existing config
  onRPointUpdate?: (rPointCost: number, updatedBalance: string, timestamp: number) => void;
}

// Added SSE callback
onUpdateRPoint: (rPointCost: number, updatedBalance: string, timestamp: number) => {
  console.log('[useChatStream] 🪙 RPoint update received:', { rPointCost, updatedBalance, timestamp });
  
  // Emit RPoint update event để các component khác có thể listen
  if (config.onRPointUpdate) {
    config.onRPointUpdate(rPointCost, updatedBalance, timestamp);
  }
},
```

### **3. RPoint Update Hook**

#### **useRPointUpdate.ts - Dedicated Hook**:
```typescript
export interface RPointUpdateOptions {
  useUpdatedBalance?: boolean; // true: use updatedBalance, false: deduct rPointCost
  onUpdate?: (newBalance: number, cost: number, timestamp: number) => void;
  onError?: (error: string) => void;
}

export const useRPointUpdate = (options: RPointUpdateOptions = {}) => {
  const { userRPoints, setUserRPoints, deductPoints } = useRPoint();
  const { useUpdatedBalance = true, onUpdate, onError } = options;

  const handleRPointUpdate = useCallback((
    rPointCost: number, 
    updatedBalance: string, 
    timestamp: number
  ) => {
    try {
      const newBalance = parseInt(updatedBalance, 10);
      
      if (useUpdatedBalance) {
        // Method 1: Set toàn bộ balance từ updatedBalance
        setUserRPoints(newBalance);
        onUpdate?.(newBalance, rPointCost, timestamp);
      } else {
        // Method 2: Trừ rPointCost từ balance hiện tại
        const success = deductPoints(rPointCost);
        if (success) {
          onUpdate?.(userRPoints - rPointCost, rPointCost, timestamp);
        } else {
          onError?.(`Insufficient balance: ${rPointCost} > ${userRPoints}`);
        }
      }
    } catch (error) {
      onError?.(`Failed to process R-Point update: ${error}`);
    }
  }, [userRPoints, setUserRPoints, deductPoints, useUpdatedBalance, onUpdate, onError]);

  return {
    currentBalance: userRPoints,
    handleRPointUpdate,
    checkSufficientBalance: (amount: number) => userRPoints >= amount,
    formatRPoint: (amount: number) => new Intl.NumberFormat('vi-VN').format(amount)
  };
};
```

### **4. ChatPanel Integration**

#### **ChatPanel.tsx - Real-time Updates**:
```typescript
// RPoint update handler
const { handleRPointUpdate, formatRPoint } = useRPointUpdate({
  useUpdatedBalance: true, // Sử dụng updatedBalance method
  onUpdate: (newBalance, cost, timestamp) => {
    console.log('[ChatPanel] R-Point updated:', {
      newBalance: formatRPoint(newBalance),
      cost: formatRPoint(cost),
      timestamp: new Date(timestamp).toLocaleString()
    });
    
    // Show notification
    addNotification('info', 
      `R-Point đã được trừ ${formatRPoint(cost)}. Số dư hiện tại: ${formatRPoint(newBalance)}`, 
      3000
    );
  },
  onError: (error) => {
    addNotification('error', `Lỗi cập nhật R-Point: ${error}`, 5000);
  }
});

// Pass to useChatStream
const chatStream = useChatStream({
  // ... other config
  onRPointUpdate: handleRPointUpdate, // Real-time handler
});
```

## 🎯 Luồng Hoạt Động

### **Real-time R-Point Update Flow**:
```
1. User action triggers R-Point deduction on server
2. Server sends SSE event: update_rpoint
3. ChatSSEService receives event → handleUpdateRPointEvent()
4. useChatStream processes → calls config.onRPointUpdate()
5. ChatPanel useRPointUpdate → handleRPointUpdate()
6. Two update methods available:
   a) updatedBalance: setUserRPoints(newBalance)
   b) rPointCost: deductPoints(cost)
7. UI updates immediately with new balance
8. Notification shows deduction details
```

### **Event Processing**:
```
SSE Event → ChatSSEService → useChatStream → ChatPanel → useRPointUpdate → RPointContext → UI Update
```

## 🔍 Two Update Methods

### **Method 1: updatedBalance (Recommended)**
```typescript
// Pros: Accurate, server-authoritative, handles edge cases
// Cons: None
useRPointUpdate({ useUpdatedBalance: true })

// Flow: Server calculates final balance → Client sets exact balance
setUserRPoints(parseInt(updatedBalance, 10));
```

### **Method 2: rPointCost Deduction**
```typescript
// Pros: Shows explicit deduction amount
// Cons: Potential sync issues, client-side calculation
useRPointUpdate({ useUpdatedBalance: false })

// Flow: Client deducts cost from current balance
const success = deductPoints(rPointCost);
```

## 🎨 UI/UX Features

### **Real-time Feedback**:
- **Immediate Update**: Balance updates instantly when SSE event received
- **Visual Notification**: Shows deduction amount and new balance
- **Error Handling**: Displays errors if update fails
- **Formatted Display**: Vietnamese number formatting (1,000,000)

### **Notification Examples**:
```typescript
// Success notification
"R-Point đã được trừ 1,396. Số dư hiện tại: 999,599,026"

// Error notification  
"Lỗi cập nhật R-Point: Invalid updatedBalance: abc"
```

## 🧪 Testing Scenarios

### **Normal Flow**:
- [ ] SSE event received → Balance updates immediately
- [ ] Notification shows correct deduction amount
- [ ] ViewHeader displays new balance
- [ ] No console errors

### **Edge Cases**:
- [ ] Invalid updatedBalance → Error notification
- [ ] Network issues → Graceful error handling
- [ ] Multiple rapid updates → All processed correctly
- [ ] Large numbers → Proper formatting

### **Integration**:
- [ ] Works with existing RPoint context
- [ ] Compatible with ViewHeader display
- [ ] Maintains localStorage sync
- [ ] Thread switching preserves balance

## 🚀 Benefits

### **Real-time Experience**:
- ✅ **Instant Updates**: No page refresh needed
- ✅ **Accurate Balance**: Server-authoritative updates
- ✅ **Visual Feedback**: Clear deduction notifications
- ✅ **Error Resilience**: Graceful error handling

### **Technical Advantages**:
- ✅ **Flexible Methods**: Choose updatedBalance or deduction
- ✅ **Type Safety**: Full TypeScript support
- ✅ **Reusable Hook**: Can be used in other components
- ✅ **Performance**: Efficient real-time updates

### **User Experience**:
- ✅ **Transparency**: Users see exactly what was deducted
- ✅ **Confidence**: Real-time balance updates build trust
- ✅ **Convenience**: No manual refresh required
- ✅ **Clarity**: Formatted numbers easy to read

## 📊 Performance Considerations

### **Optimizations**:
- **Debouncing**: Not needed for R-Point updates (infrequent)
- **Batching**: Single update per SSE event
- **Memory**: Minimal overhead with useCallback
- **Network**: Leverages existing SSE connection

### **Monitoring**:
- Console logs for debugging
- Error tracking for failed updates
- Performance metrics for update latency
- User feedback through notifications

Hệ thống R-Point real-time update đã được implement hoàn chỉnh với UX tối ưu! 🪙✨
