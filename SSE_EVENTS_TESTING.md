# Hướng Dẫn Test SSE Events Mới

## Tổng Quan

Đã cập nhật hệ thống SSE để hỗ trợ các sự kiện mới theo yêu cầu:

1. **tool_call_start** - Hiển thị trạng thái "thinking"
2. **tool_call_end** - K<PERSON><PERSON> thúc trạng thái "thinking"  
3. **llm_stream_end** - <PERSON><PERSON> lý khác nhau cho worker/supervisor
4. **message_created** - Gán messageId cho supervisor messages
5. **stream_session_end** - Kết thúc toàn bộ session SSE

## Cách Test

### 1. Sử dụng Demo Page

Truy cập: `http://localhost:3000/demo/sse-events`

Demo page cung cấp:
- Status panel hiển thị trạng thái real-time
- Controls để tạo thread và gửi message
- Message display với collapse/expand cho worker messages
- Error handling và retry functionality

### 2. <PERSON><PERSON><PERSON> Tra Các Sự Kiện

#### Tool Call Events
```
Khi gửi message → tool_call_start → isThinking = true → UI hiển thị "Thinking: Yes"
Sau khi tool call xong → tool_call_end → isThinking = false → UI hiển thị "Thinking: No"
```

#### LLM Stream End Events
```
llm_stream_end với role="worker" → Message được collapse
llm_stream_end với role="supervisor" → Message hiển thị bình thường
```

#### Message Created Event
```
message_created với role="assistant" → messageId được gán cho supervisor message
```

#### Stream Session End
```
stream_session_end → Tất cả states được reset (streaming, loading, thinking, connected)
```

### 3. Kiểm Tra Console Logs

Mở Developer Tools và xem console để theo dõi:

```javascript
// Tool call events
[useChatStream] 🤔 Tool call started for role: supervisor
[useChatStream] ✅ Tool call ended for role: supervisor

// LLM stream end events  
[useChatStream] 🏁 LLM STREAM ENDED for role: worker
[useChatStream] Worker stream ended - collapsing message

[useChatStream] 🏁 LLM STREAM ENDED for role: supervisor  
[useChatStream] Supervisor stream ended - keeping message visible

// Message created event
[useChatStream] 📝 Message created: {messageId, threadId, role, contentPreview}
[useChatStream] Assigning messageId to supervisor message: a60dd1e5-df21-4940-b119-1e78e16660bb

// Stream session end
[useChatStream] 🔚 STREAM SESSION ENDED: Processing complete
```

## Các File Đã Cập Nhật

### 1. ChatSSEService
- **File**: `src/shared/services/chat-sse.service.ts`
- **Thay đổi**: Thêm callbacks và handlers cho 5 sự kiện mới

### 2. useChatStream Hook  
- **File**: `src/shared/hooks/common/useChatStream.ts`
- **Thay đổi**: 
  - Thêm state `isThinking`
  - Cập nhật callbacks xử lý sự kiện mới
  - Reset `isThinking` ở tất cả chỗ cần thiết

### 3. ChatMessage Types
- **File**: `src/shared/types/chat-streaming.types.ts`  
- **Thay đổi**: Thêm properties mới cho metadata

### 4. Demo Components
- **File**: `src/shared/components/demo/SSEEventsDemo.tsx`
- **File**: `src/pages/demo/sse-events.tsx`
- **Mục đích**: Test và demo các sự kiện mới

## Kiểm Tra Tính Năng

### ✅ Tool Call Thinking State
- [ ] `isThinking` chuyển thành `true` khi nhận `tool_call_start`
- [ ] `isThinking` chuyển thành `false` khi nhận `tool_call_end`
- [ ] UI hiển thị thinking indicator đúng cách

### ✅ Worker Message Collapse
- [ ] Worker messages được đánh dấu `collapsed: true` khi nhận `llm_stream_end` với role="worker"
- [ ] UI hiển thị worker messages ở dạng collapsed
- [ ] Có thể toggle expand/collapse worker messages

### ✅ Supervisor Message Handling
- [ ] Supervisor messages hiển thị bình thường khi nhận `llm_stream_end` với role="supervisor"
- [ ] MessageId được gán từ `message_created` event cho role="assistant"
- [ ] ContentPreview được lưu trong metadata

### ✅ Stream Session End
- [ ] Tất cả states được reset khi nhận `stream_session_end`
- [ ] Connection được đóng đúng cách
- [ ] UI trở về trạng thái idle

### ✅ Error Handling
- [ ] `isThinking` được reset khi có lỗi
- [ ] Stream errors vẫn hoạt động với retry functionality
- [ ] SSE errors được xử lý đúng cách

## Lưu Ý Quan Trọng

1. **Thinking State**: Chỉ hiển thị khi có `tool_call_start` và tắt khi có `tool_call_end`

2. **Worker vs Supervisor**: 
   - Worker messages có thể collapse/expand
   - Supervisor messages luôn hiển thị đầy đủ

3. **MessageId Assignment**: 
   - Chỉ gán cho role="assistant" từ `message_created` event
   - Không gán từ `SendMessageResponse` vì không có field này

4. **Session End**: 
   - `stream_session_end` là event cuối cùng
   - Reset tất cả states và đóng connection

5. **Backward Compatibility**: 
   - Vẫn hỗ trợ `onStreamEnd` callback cũ làm fallback
   - Không breaking changes cho code hiện tại

## Troubleshooting

### Nếu không thấy thinking state:
- Kiểm tra console có log `tool_call_start/end` không
- Verify callback `onToolCallStart/End` được gọi
- Check `isThinking` state trong React DevTools

### Nếu worker messages không collapse:
- Kiểm tra `llm_stream_end` event có role="worker"
- Verify metadata có `collapsed: true`
- Check UI component có xử lý collapsed state

### Nếu messageId không được gán:
- Kiểm tra `message_created` event có role="assistant"  
- Verify callback `onMessageCreated` được gọi
- Check message có được update với messageId

## Demo Data

Để test đầy đủ, server cần gửi sequence events như sau:

```
1. connected
2. tool_call_start (role: supervisor)
3. tool_call_end (role: supervisor)  
4. stream_text_token (role: worker) - multiple times
5. llm_stream_end (role: worker)
6. stream_text_token (role: supervisor) - multiple times
7. llm_stream_end (role: supervisor)
8. message_created (role: assistant)
9. stream_session_end
```
