# Cập <PERSON>h<PERSON>t SSE Events cho Chat Streaming

## Tổ<PERSON> Quan

Cập nhật hệ thống SSE để hỗ trợ các sự kiện mới theo mẫu được cung cấp:

```
event: connected
data: {"threadId":"96bba755-082b-4d44-96d0-5e70af949c88","from":"live","timestamp":1749617326387}

id: 1749617294289-0
data: {"event":"tool_call_start","data":{"role":"supervisor"},"timestamp":1749617293326}

id: 1749617294302-0
data: {"event":"tool_call_end","data":{"role":"supervisor"},"timestamp":1749617293340}

id: 1749617295714-0
data: {"event":"llm_stream_end","data":{"role":"worker"},"timestamp":1749617294750}

id: 1749617297869-0
data: {"event":"message_created","data":{"message_id":"a60dd1e5-df21-4940-b119-1e78e16660bb","thread_id":"96bba755-082b-4d44-96d0-5e70af949c88","role":"assistant","content_preview":"The task was successfully completed by the worker. I will now provide the response to you.\n\nFor the ","has_token_data":false,"partial":false,"timestamp":1749617296905},"timestamp":1749617296905}

id: 1749617297916-0
data: {"event":"stream_session_end","data":{"reason":"Processing complete"},"timestamp":1749617296952}
```

## Các Sự Kiện Mới

### 1. `tool_call_start`
- **Mục đích**: Hiển thị trạng thái "thinking" khi agent bắt đầu gọi tool
- **Xử lý**: Bật `isThinking = true`
- **UI**: Hiển thị indicator "đang thinking"

### 2. `tool_call_end`
- **Mục đích**: Kết thúc trạng thái "thinking"
- **Xử lý**: Tắt `isThinking = false`
- **UI**: Ẩn indicator "thinking"

### 3. `llm_stream_end`
- **Mục đích**: Kết thúc stream cho một role cụ thể
- **Xử lý khác biệt**:
  - **Role 'worker'**: Message được collapse (ẩn đi, có thể mở ra xem)
  - **Role 'supervisor'**: Message hiển thị bình thường
- **UI**: Worker messages có thể toggle show/hide

### 4. `message_created`
- **Mục đích**: Gán messageId cho message từ API
- **Xử lý**: Chỉ gán cho role 'assistant' (supervisor)
- **Dữ liệu**: messageId, threadId, role, contentPreview

### 5. `stream_session_end`
- **Mục đích**: Kết thúc toàn bộ session SSE
- **Xử lý**: Reset tất cả state (streaming, loading, thinking, connected)
- **UI**: Hoàn tất quá trình chat

## Thay Đổi Code

### 1. ChatSSEService (`src/shared/services/chat-sse.service.ts`)

#### Thêm Callbacks Mới:
```typescript
export interface SSECallbacks {
  // ... existing callbacks
  onToolCallStart?: (role: string) => void;
  onToolCallEnd?: (role: string) => void;
  onLLMStreamEnd?: (role: string) => void;
  onMessageCreated?: (messageId: string, threadId: string, role: string, contentPreview: string) => void;
  onStreamSessionEnd?: (reason: string) => void;
}
```

#### Thêm Event Handlers:
- `handleToolCallStartEvent()`
- `handleToolCallEndEvent()`
- `handleLLMStreamEndEvent()`
- `handleMessageCreatedEvent()`
- `handleStreamSessionEndEvent()`

#### Cập Nhật Event Routing:
```typescript
switch (eventData.event) {
  case 'tool_call_start':
    this.handleToolCallStartEvent(event);
    break;
  case 'tool_call_end':
    this.handleToolCallEndEvent(event);
    break;
  case 'llm_stream_end':
    this.handleLLMStreamEndEvent(event);
    break;
  case 'message_created':
    this.handleMessageCreatedEvent(event);
    break;
  case 'stream_session_end':
    this.handleStreamSessionEndEvent(event);
    break;
}
```

### 2. useChatStream Hook (`src/shared/hooks/common/useChatStream.ts`)

#### Thêm State Mới:
```typescript
const [isThinking, setIsThinking] = useState(false);
```

#### Cập Nhật Return Interface:
```typescript
export interface UseChatStreamReturn {
  // ... existing properties
  isThinking: boolean;
}
```

#### Thêm SSE Callbacks:
```typescript
sseServiceRef.current.setCallbacks({
  onToolCallStart: (role: string) => {
    setIsThinking(true);
  },
  
  onToolCallEnd: (role: string) => {
    setIsThinking(false);
  },
  
  onLLMStreamEnd: (role: string) => {
    if (role === 'worker') {
      // Collapse worker message
      const collapsedMessage = {
        ...currentStreamingMessageRef.current,
        metadata: { ...metadata, collapsed: true }
      };
    } else if (role === 'supervisor') {
      // Keep supervisor message visible
      const finalMessage = {
        ...currentStreamingMessageRef.current,
        status: MessageStatus.COMPLETED
      };
    }
  },
  
  onMessageCreated: (messageId, threadId, role, contentPreview) => {
    if (role === 'assistant') {
      // Gán messageId cho supervisor message
      const updatedMessage = {
        ...currentStreamingMessageRef.current,
        messageId: messageId,
        metadata: { ...metadata, apiMessageId: messageId, contentPreview }
      };
    }
  },
  
  onStreamSessionEnd: (reason: string) => {
    // Reset tất cả state
    setIsStreaming(false);
    setIsLoading(false);
    setIsThinking(false);
    setIsConnected(false);
    // ...
  }
});
```

### 3. ChatMessage Types (`src/shared/types/chat-streaming.types.ts`)

#### Cập Nhật ChatMessage Interface:
```typescript
export interface ChatMessage {
  // ... existing properties
  messageId?: string; // Message ID từ API
  
  metadata?: {
    // ... existing metadata
    collapsed?: boolean; // Cho worker messages
    apiMessageId?: string; // Message ID từ API response
    contentPreview?: string; // Content preview từ message_created
  };
}
```

## Luồng Xử Lý Mới

### 1. Tool Call Flow:
```
tool_call_start → isThinking = true → UI shows "thinking"
tool_call_end → isThinking = false → UI hides "thinking"
```

### 2. Message Completion Flow:
```
llm_stream_end (worker) → collapse message
llm_stream_end (supervisor) → keep message visible
message_created (assistant) → assign messageId
stream_session_end → reset all states
```

### 3. UI Behavior:
- **Thinking State**: Hiển thị spinner/indicator khi `isThinking = true`
- **Worker Messages**: Có thể collapse/expand
- **Supervisor Messages**: Luôn hiển thị đầy đủ
- **Message IDs**: Được gán từ `message_created` event

## Testing

Để test các sự kiện mới:

1. **Tool Call Events**: Kiểm tra thinking indicator hiển thị/ẩn
2. **LLM Stream End**: Kiểm tra worker messages collapse, supervisor messages visible
3. **Message Created**: Kiểm tra messageId được gán cho supervisor messages
4. **Stream Session End**: Kiểm tra tất cả states được reset

## Lưu Ý

- Tất cả chỗ `setIsStreaming(false)` đều cần thêm `setIsThinking(false)`
- Worker messages cần UI component để toggle collapse/expand
- MessageId chỉ được gán cho role 'assistant' từ `message_created` event
- `stream_session_end` là event cuối cùng, kết thúc toàn bộ session
