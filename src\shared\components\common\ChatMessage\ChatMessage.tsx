import React, { useState, useEffect, useRef } from 'react';
import { StreamingText } from '../StreamingText';
import { ReplyIndicator } from '../ReplyIndicator';
import {
  getAvatarForRole,
  getRoleDisplayName,
  shouldShowAvatarForRole,
  shouldShowBoxForRole,
  isRightAlignedRole,
  shouldShowRoleName,
  getRoleClasses,
  type ChatRole
} from '@/shared/utils/avatarMapping';
import { parseReplyContent } from '@/shared/utils/replyParser';
import { ReplyMessage } from '../ReplyPreview/ReplyPreview';

// Re-export ChatRole for convenience
export type { ChatRole };

export interface ChatMessageProps {
  /**
   * Nội dung tin nhắn
   */
  content: React.ReactNode;

  /**
   * Người gửi tin nhắn - hỗ trợ thêm role supervisor và worker
   */
  sender: ChatRole;

  /**
   * Thời gian gửi tin nhắn
   */
  timestamp: Date;

  /**
   * URL avatar (chỉ hiển thị cho tin nhắn từ AI)
   */
  avatar?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Có đang streaming không (để hiệu ứng typing)
   */
  isStreaming?: boolean;

  /**
   * Có đang chờ tokens mới không
   */
  isWaiting?: boolean;

  /**
   * Role cụ thể cho SSE events (supervisor/worker)
   */
  role?: string;

  /**
   * Callback khi click reply
   */
  onReply?: (message: ReplyMessage) => void;

  /**
   * ID của tin nhắn (để reply)
   */
  messageId?: string;

  /**
   * Callback khi click vào reply indicator để focus message
   */
  onFocusMessage?: (messageId: string) => void;

  /**
   * Có collapsed không (cho worker messages)
   */
  isCollapsed?: boolean;

  /**
   * Callback khi toggle collapse state
   */
  onToggleCollapse?: () => void;

  /**
   * Content preview khi collapsed
   */
  contentPreview?: string;
}

/**
 * Component hiển thị tin nhắn trong chat
 */
const ChatMessage: React.FC<ChatMessageProps> = props => {
  const {
    content,
    sender,
    timestamp,
    className = '',
    isStreaming = false,
    isWaiting = false,
    avatar,
    onReply,
    messageId,
    onFocusMessage,
    isCollapsed = false,
    onToggleCollapse,
    contentPreview
  } = props;
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const messageRef = useRef<HTMLDivElement>(null);

  console.log('[ChatMessage] Rendering message:', timestamp);

  // Parse content để extract reply context
  const contentString = typeof content === 'string' ? content : String(content);
  const parsedContent = parseReplyContent(contentString);
  const hasReply = parsedContent.hasReply;
  const actualContent = parsedContent.actualMessage;
  const replyContent = parsedContent.replyContent;
  const replyMessageId = parsedContent.replyMessageId;

  // Animation khi component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50); // Delay nhỏ để animation mượt hơn

    return () => clearTimeout(timer);
  }, []);
  
  // Format timestamp
  // const formatTime = (date: Date): string => {
  //   return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  // };

  // Animation classes
  const animationClasses = isVisible
    ? 'opacity-100 translate-y-0 scale-100'
    : 'opacity-0 translate-y-2 scale-95';

  const streamingClasses = isStreaming
    ? 'animate-pulse'
    : '';

  // Sử dụng avatar mapping utilities
  const avatarConfig = getAvatarForRole(sender, avatar);
  const avatarUrl = avatar || avatarConfig.url;
  const shouldShowAvatar = shouldShowAvatarForRole(sender);
  const shouldShowBox = shouldShowBoxForRole(sender);
  const isRightAligned = isRightAlignedRole(sender);
  const showRoleName = shouldShowRoleName(sender);
  const roleClasses = getRoleClasses(sender);
  const roleDisplayName = getRoleDisplayName(sender);

  // Handler cho reply
  const handleReply = () => {
    if (onReply && !hasReply) { // Không cho phép reply message đã có reply context
      const replyMessage: ReplyMessage = {
        content: actualContent, // Sử dụng actual content, không bao gồm reply context
        sender,
        timestamp
      };

      // Chỉ thêm id nếu messageId có giá trị
      if (messageId) {
        replyMessage.id = messageId;
      }

      onReply(replyMessage);
    }
  };

  // Handler cho focus message
  const handleFocusMessage = (targetMessageId?: string) => {
    if (onFocusMessage && targetMessageId) {
      onFocusMessage(targetMessageId);
    }
  };

  return (
    <div
      ref={messageRef}
      className={`w-full ${className} group`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      id={messageId ? `message-${messageId}` : undefined}
    >
      {/* Hiển thị role name cho supervisor/worker */}
      {showRoleName && (
        <div className={roleClasses.roleName}>
          {roleDisplayName}
        </div>
      )}

      {/* Hiển thị reply indicator nếu message có reply context */}
      {hasReply && replyContent && (
        <ReplyIndicator
          replyContent={replyContent}
          {...(replyMessageId && { replyMessageId })} // Chỉ pass nếu có giá trị
          onReplyClick={handleFocusMessage}
          className="mb-2"
        />
      )}

      <div
        className={`${roleClasses.container} relative
          transform transition-all duration-300 ease-out ${animationClasses}`}
      >
        {/* Avatar bên trái cho AI roles */}
        {shouldShowAvatar && !isRightAligned && (
          <div className="flex-shrink-0">
            <div className={roleClasses.avatar}>
              <img
                src={avatarUrl}
                alt={avatarConfig.alt}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        {/* Nội dung tin nhắn với reply button */}
        <div className={`${roleClasses.content} flex items-center gap-2`}>
          {/* Reply button cho user - hiển thị ở đằng trước (bên trái) */}
          {onReply && isRightAligned && (
            <div className="flex-shrink-0 w-8 flex items-center justify-center">
              <button
                onClick={handleReply}
                className={`p-1.5 rounded-full bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20
                  text-red-500 hover:text-red-600 dark:hover:text-red-400
                  transition-all duration-200 shadow-md border border-gray-200 dark:border-gray-600
                  ${isHovered && !hasReply ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                title="Reply to this message"
                disabled={hasReply}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
              </button>
            </div>
          )}

          {/* Message content */}
          <div className="flex-1 min-w-0">
            {/* Collapsed state cho worker messages */}
            {isCollapsed && sender === 'worker' ? (
              <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/30 border border-yellow-200 dark:border-yellow-800/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">
                      Worker process completed
                    </span>
                  </div>
                  {onToggleCollapse && (
                    <button
                      onClick={onToggleCollapse}
                      className="text-xs bg-yellow-200 hover:bg-yellow-300 dark:bg-yellow-800 dark:hover:bg-yellow-700 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded transition-colors"
                    >
                      Expand
                    </button>
                  )}
                </div>
                {contentPreview && (
                  <div className="mt-2 text-xs text-yellow-600 dark:text-yellow-400">
                    Preview: {contentPreview.slice(0, 100)}...
                  </div>
                )}
              </div>
            ) : shouldShowBox ? (
              // User message với box
              <div
                className={`p-3 rounded-lg break-words overflow-hidden ${streamingClasses}
                  bg-primary text-white rounded-tr-none`}
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto'
                }}
              >
                {/* Collapse button cho worker messages */}
                {sender === 'worker' && onToggleCollapse && (
                  <div className="flex justify-end mb-2">
                    <button
                      onClick={onToggleCollapse}
                      className="text-xs bg-white/20 hover:bg-white/30 text-white px-2 py-1 rounded transition-colors"
                    >
                      Collapse
                    </button>
                  </div>
                )}
                <div className="whitespace-pre-wrap break-words overflow-hidden">
                  {isStreaming && typeof actualContent === 'string' ? (
                    <StreamingText
                      text={actualContent}
                      isStreaming={isStreaming}
                      isWaiting={isWaiting}
                      showCursor={true}
                      cursorStyle="blink"
                    />
                  ) : (
                    actualContent
                  )}
                </div>
              </div>
            ) : (
              // AI message không có box, hiển thị trực tiếp
              <div
                className={`break-words overflow-hidden ${streamingClasses}
                  text-gray-900 dark:text-gray-100`}
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto'
                }}
              >
                {/* Collapse button cho worker messages */}
                {sender === 'worker' && onToggleCollapse && (
                  <div className="flex justify-end mb-2">
                    <button
                      onClick={onToggleCollapse}
                      className="text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded transition-colors"
                    >
                      Collapse
                    </button>
                  </div>
                )}
                <div className="whitespace-pre-wrap break-words overflow-hidden">
                  {isStreaming && typeof actualContent === 'string' ? (
                    <StreamingText
                      text={actualContent}
                      isStreaming={isStreaming}
                      isWaiting={isWaiting}
                      showCursor={true}
                      cursorStyle="blink"
                    />
                  ) : (
                    actualContent
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Reply button cho AI - hiển thị ở đằng sau (bên phải) */}
          {onReply && !isRightAligned && (
            <div className="flex-shrink-0 w-8 flex items-center justify-center">
              <button
                onClick={handleReply}
                className={`p-1.5 rounded-full bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20
                  text-red-500 hover:text-red-600 dark:hover:text-red-400
                  transition-all duration-200 shadow-md border border-gray-200 dark:border-gray-600
                  ${isHovered && !hasReply ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                title="Reply to this message"
                disabled={hasReply}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Avatar bên phải cho user (nếu cần) */}
        {shouldShowAvatar && isRightAligned && (
          <div className="flex-shrink-0">
            <div className={roleClasses.avatar}>
              <img
                src={avatarUrl}
                alt={avatarConfig.alt}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
