/**
 * Demo component để test các SSE events mới
 */

import React, { useState } from 'react';
import { useChatStream } from '@/shared/hooks/common/useChatStream';

const SSEEventsDemo: React.FC = () => {
  const [testMessage, setTestMessage] = useState('Test message for new SSE events');

  const chatStream = useChatStream({
    getAuthToken: () => 'demo-token',
    debug: true,
    threadEvents: {
      onThreadCreated: (threadId, threadName) => {
        console.log('Demo: Thread created:', { threadId, threadName });
      },
      onThreadLoaded: (threadId, threadName) => {
        console.log('Demo: Thread loaded:', { threadId, threadName });
      }
    }
  });

  const {
    messages,
    isStreaming,
    isLoading,
    isThinking,
    threadId,
    threadName,
    isConnected,
    error,
    streamError,
    sendMessage,
    stopStreaming,
    createNewThread,
    retryLastMessage
  } = chatStream;

  const handleSendMessage = async () => {
    if (!testMessage.trim()) return;
    
    try {
      await sendMessage(testMessage);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleCreateThread = async () => {
    try {
      const result = await createNewThread('Demo Thread for SSE Events');
      console.log('Thread created:', result);
    } catch (error) {
      console.error('Failed to create thread:', error);
    }
  };

  const renderMessage = (message: any, index: number) => {
    const isCollapsed = message.metadata?.collapsed;
    const isWorker = message.sender === 'worker';
    
    return (
      <div 
        key={message.id} 
        className={`p-4 mb-4 rounded-lg border ${
          message.sender === 'user' 
            ? 'bg-blue-50 border-blue-200 ml-8' 
            : message.sender === 'worker'
            ? 'bg-yellow-50 border-yellow-200 mr-8'
            : 'bg-gray-50 border-gray-200 mr-8'
        }`}
      >
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-sm">
              {message.sender.toUpperCase()}
            </span>
            {message.messageId && (
              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                ID: {message.messageId.slice(-8)}
              </span>
            )}
            {isWorker && (
              <button 
                className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
                onClick={() => {
                  // Toggle collapse state (demo only)
                  console.log('Toggle collapse for worker message:', message.id);
                }}
              >
                {isCollapsed ? 'Expand' : 'Collapse'}
              </button>
            )}
          </div>
          <span className="text-xs text-gray-500">
            {message.timestamp.toLocaleTimeString()}
          </span>
        </div>
        
        {isCollapsed ? (
          <div className="text-sm text-gray-600 italic">
            Worker process completed (click Expand to view details)
            {message.metadata?.contentPreview && (
              <div className="mt-1 text-xs">
                Preview: {message.metadata.contentPreview.slice(0, 100)}...
              </div>
            )}
          </div>
        ) : (
          <div className="text-sm">
            {typeof message.content === 'string' ? message.content : JSON.stringify(message.content)}
          </div>
        )}
        
        <div className="mt-2 text-xs text-gray-500">
          Status: {message.status}
          {message.metadata?.processingTime && (
            <span className="ml-2">
              Processing: {message.metadata.processingTime}ms
            </span>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">SSE Events Demo</h1>
      
      {/* Status Panel */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-3">Connection Status</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className={`p-2 rounded ${isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            Connected: {isConnected ? 'Yes' : 'No'}
          </div>
          <div className={`p-2 rounded ${isStreaming ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'}`}>
            Streaming: {isStreaming ? 'Yes' : 'No'}
          </div>
          <div className={`p-2 rounded ${isLoading ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'}`}>
            Loading: {isLoading ? 'Yes' : 'No'}
          </div>
          <div className={`p-2 rounded ${isThinking ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-600'}`}>
            Thinking: {isThinking ? 'Yes' : 'No'}
          </div>
        </div>
        
        {threadId && (
          <div className="mt-3 text-sm">
            <strong>Thread:</strong> {threadName} ({threadId.slice(-8)})
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-3">Controls</h2>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleCreateThread}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            disabled={isLoading}
          >
            Create Thread
          </button>
          
          <button
            onClick={handleSendMessage}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            disabled={isLoading || !threadId || !testMessage.trim()}
          >
            Send Message
          </button>
          
          <button
            onClick={stopStreaming}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            disabled={!isStreaming}
          >
            Stop Streaming
          </button>
          
          {streamError && (
            <button
              onClick={retryLastMessage}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Retry Last Message
            </button>
          )}
        </div>
        
        <div className="mt-3">
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter test message..."
            className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !isLoading && threadId && testMessage.trim()) {
                handleSendMessage();
              }
            }}
          />
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="text-red-800 font-semibold">Error:</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Stream Error Display */}
      {streamError && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <h3 className="text-orange-800 font-semibold">Stream Error:</h3>
          <p className="text-orange-700">{streamError.message}</p>
          {streamError.details && (
            <pre className="mt-2 text-xs text-orange-600 bg-orange-100 p-2 rounded overflow-auto">
              {JSON.stringify(streamError.details, null, 2)}
            </pre>
          )}
        </div>
      )}

      {/* Messages */}
      <div className="bg-white border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Messages ({messages.length})</h2>
        <div className="max-h-96 overflow-y-auto">
          {messages.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No messages yet. Create a thread and send a message to test SSE events.</p>
          ) : (
            messages.map(renderMessage)
          )}
        </div>
      </div>

      {/* Event Log */}
      <div className="mt-6 bg-gray-50 border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Expected SSE Events</h2>
        <div className="text-sm space-y-2">
          <div><strong>tool_call_start:</strong> Sets isThinking = true</div>
          <div><strong>tool_call_end:</strong> Sets isThinking = false</div>
          <div><strong>llm_stream_end (worker):</strong> Collapses worker message</div>
          <div><strong>llm_stream_end (supervisor):</strong> Keeps supervisor message visible</div>
          <div><strong>message_created:</strong> Assigns messageId to supervisor message</div>
          <div><strong>stream_session_end:</strong> Resets all states</div>
        </div>
      </div>
    </div>
  );
};

export default SSEEventsDemo;
