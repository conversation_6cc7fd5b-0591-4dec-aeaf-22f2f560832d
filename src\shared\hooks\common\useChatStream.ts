/**
 * useChatStream Hook
 * Hook chính để quản lý chat streaming với luồng mới
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { flushSync } from 'react-dom';
import { ChatApiService } from '@/shared/services/chat-api.service';
import { ChatSSEService } from '@/shared/services/chat-sse.service';
import { chatConfigService } from '@/shared/services/chat-config.service';
import {
  ChatMessage,
  MessageStatus,
  SendMessageResponse,
  GetMessagesQuery,
  convertHistoryMessagesToChatMessages,
  GetMessagesResponseData,
  mapRoleToMessageSender,
} from '@/shared/types/chat-streaming.types';
import { v4 as uuidv4 } from 'uuid';
import { chatApiSingleton } from '@/shared/utils/chatApiSingleton';
import { ThreadsService } from '@/modules/threads/services';
import { isThreadDeleted } from '@/modules/threads/services/deleted-thread-tracker.service';

/**
 * Thread event callbacks
 */
export interface ThreadEventCallbacks {
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadLoaded?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}

/**
 * Configuration cho useChatStream hook
 */
export interface UseChatStreamConfig {
  agentId?: string;
  apiBaseUrl?: string;
  sseBaseUrl?: string;
  alwaysApproveToolCall?: boolean;
  getAuthToken: () => string | Promise<string>;
  debug?: boolean;

  /**
   * Configuration cho message history
   */
  messageHistory?: {
    pageSize?: number;
    autoLoad?: boolean;
    timeout?: number;
  };

  /**
   * Thread event callbacks
   */
  threadEvents?: ThreadEventCallbacks;
}

/**
 * Return type cho useChatStream hook
 */
export interface UseChatStreamReturn {
  // State
  messages: ChatMessage[];
  isStreaming: boolean;
  isLoading: boolean;
  isThinking: boolean;
  currentStreamingText: string;
  currentRunId: string | null;
  threadId: string | null;
  threadName: string | null;
  isCreatingThread: boolean;
  isLoadingThreads: boolean;

  // Message History
  historyMessages: ChatMessage[];
  isLoadingHistory: boolean;
  isLoadingMoreHistory: boolean;
  hasMoreHistory: boolean;
  historyError: string | null;
  totalHistoryItems: number;

  // Actions
  sendMessage: (content: string) => Promise<void>;
  stopStreaming: () => Promise<void>;
  clearMessages: () => void;
  createNewThread: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  loadLatestThread: () => Promise<void>;
  loadSpecificThread: (threadId: string) => Promise<void>;
  switchToThread: (threadId: string) => Promise<void>;
  getCurrentThreadId: () => string | null;
  updateThreadName: (threadId: string, newName: string) => Promise<void>;
  retryLastMessage: () => Promise<void>;

  // Message History Actions
  loadMoreHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;

  // Status
  isConnected: boolean;
  error: string | null;
  streamError: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
}

/**
 * useChatStream Hook
 */
export function useChatStream(config: UseChatStreamConfig): UseChatStreamReturn {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingText, setCurrentStreamingText] = useState('');
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [threadName, setThreadName] = useState<string | null>(null);
  const [isCreatingThread, setIsCreatingThread] = useState(false);
  const [isLoadingThreads, setIsLoadingThreads] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentRole, setCurrentRole] = useState<string>('ai'); // Track current role từ SSE
  const [isThinking, setIsThinking] = useState(false); // Track thinking state từ tool_call_start/end

  // Stream error state
  const [streamError, setStreamError] = useState<{
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null>(null);

  // Message History State
  const [historyMessages, setHistoryMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isLoadingMoreHistory, setIsLoadingMoreHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1);
  const [totalHistoryItems, setTotalHistoryItems] = useState(0);

  // Services refs
  const apiServiceRef = useRef<ChatApiService | null>(null);
  const sseServiceRef = useRef<ChatSSEService | null>(null);
  const currentStreamingMessageRef = useRef<ChatMessage | null>(null);
  const hasLoadedInitialThreadRef = useRef<boolean>(false);
  const lastMessageContentRef = useRef<string>(''); // Lưu content để retry

  // Debug refs for token tracking
  const tokenCountRef = useRef<number>(0);
  const lastTokenTimeRef = useRef<number>(0);

  // Message History refs
  const currentThreadIdRef = useRef<string | null>(null);
  const isHistoryLoadingRef = useRef(false);
  const historyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function refs to avoid circular dependencies
  const stopStreamingRef = useRef<(() => Promise<void>) | null>(null);

  // Initialize services
  useEffect(() => {
    const chatConfig = chatConfigService.getConfig();
    const finalConfig = {
      agentId: config.agentId || chatConfig.agentId,
      apiBaseUrl: config.apiBaseUrl || chatConfig.apiBaseUrl,
      sseBaseUrl: config.sseBaseUrl || chatConfig.sseBaseUrl,
      alwaysApproveToolCall: config.alwaysApproveToolCall ?? chatConfig.alwaysApproveToolCall,
      debug: config.debug ?? chatConfig.debug
    };

    // Initialize API service
    apiServiceRef.current = new ChatApiService(
      finalConfig.apiBaseUrl,
      30000, // timeout
      finalConfig.debug
    );

    // Initialize SSE service
    sseServiceRef.current = new ChatSSEService(
      finalConfig.sseBaseUrl,
    );

    // Setup SSE callbacks
    sseServiceRef.current.setCallbacks({
      onConnected: (data) => {
        console.log('[useChatStream] SSE Connected:', data);
        setIsConnected(true);
        setError(null);
      },

      onToolCallStart: (role: string) => {
        console.log('[useChatStream] 🤔 Tool call started for role:', role);
        setIsThinking(true);
      },

      onToolCallEnd: (role: string) => {
        console.log('[useChatStream] ✅ Tool call ended for role:', role);
        setIsThinking(false);
      },
      
      onTextToken: (text: string, role: string) => {
        // Track token frequency
        const now = Date.now();
        const timeSinceLastToken = now - lastTokenTimeRef.current;
        tokenCountRef.current += 1;
        lastTokenTimeRef.current = now;

        console.log('[useChatStream] 🔥 TEXT TOKEN RECEIVED:', {
          text,
          textLength: text.length,
          role,
          isStreaming,
          isLoading,
          tokenCount: tokenCountRef.current,
          timeSinceLastToken,
          tokensPerSecond: timeSinceLastToken > 0 ? (1000 / timeSinceLastToken).toFixed(2) : 'N/A',
          timestamp: new Date().toISOString()
        });
        console.log('[useChatStream] Current streaming message ref:', currentStreamingMessageRef.current);

        // Map role từ SSE event thành MessageSender
        const mappedSender = mapRoleToMessageSender(role);
        console.log('[useChatStream] Mapped sender:', mappedSender);

        // Nếu đây là token đầu tiên, bật streaming
        if (!isStreaming) {
          console.log('[useChatStream] First token received, starting streaming (keeping loading until stream ends)');
          setIsStreaming(true);
        }

        // Tạo message mới nếu chưa có hoặc role thay đổi
        if (!currentStreamingMessageRef.current) {
          console.log('[useChatStream] 🆕 CREATING FIRST MESSAGE for role:', role, 'mapped to:', mappedSender);

          // Tạo message mới
          const newMessage: ChatMessage = {
            id: uuidv4(),
            content: text,
            sender: mappedSender,
            timestamp: new Date(),
            status: MessageStatus.STREAMING,
            threadId: threadId!
          };

          // Force immediate UI update cho message mới
          flushSync(() => {
            setMessages(prev => [...prev, newMessage]);
            setCurrentStreamingText(text);
          });

          currentStreamingMessageRef.current = newMessage;
          console.log('[useChatStream] ✅ Created first message immediately:', newMessage);

        } else if (currentStreamingMessageRef.current.sender !== mappedSender) {
          console.log('[useChatStream] 🔄 ROLE CHANGED - Finalizing current and creating new message');

          // Finalize message hiện tại
          const finalizedMessage = {
            ...currentStreamingMessageRef.current,
            status: MessageStatus.COMPLETED
          };

          // Tạo message mới cho role mới
          const newMessage: ChatMessage = {
            id: uuidv4(),
            content: text,
            sender: mappedSender,
            timestamp: new Date(),
            status: MessageStatus.STREAMING,
            threadId: threadId!
          };

          // Force immediate UI update
          flushSync(() => {
            setMessages(prev => [
              ...prev.map(msg => msg.id === currentStreamingMessageRef.current?.id ? finalizedMessage : msg),
              newMessage
            ]);
            setCurrentStreamingText(text);
          });

          currentStreamingMessageRef.current = newMessage;
          console.log('[useChatStream] ✅ Role changed - created new message:', newMessage);

        } else {
          // Append text vào message hiện tại (cùng role)
          const previousContent = typeof currentStreamingMessageRef.current.content === 'string'
            ? currentStreamingMessageRef.current.content
            : '';

          console.log('[useChatStream] 📝 Appending text:', {
            text,
            textLength: text.length,
            previousLength: previousContent.length,
            isSmallToken: text.length <= 3,
            isLargeToken: text.length > 10,
            timestamp: new Date().toISOString()
          });

          // Update target content (tổng content cuối cùng)
          const newTargetContent = previousContent + text;

          console.log('[useChatStream] 📝 Token received:', {
            textLength: text.length,
            newTotalLength: newTargetContent.length,
            text: text.length > 50 ? text.substring(0, 50) + '...' : text
          });

          // Simple direct update - append text immediately
          currentStreamingMessageRef.current.content += text;

          // Force immediate UI update
          setMessages(prev => prev.map(msg =>
            msg.id === currentStreamingMessageRef.current?.id
              ? {
                  ...msg,
                  content: currentStreamingMessageRef.current!.content
                }
              : msg
          ));

          setCurrentStreamingText(
            typeof currentStreamingMessageRef.current.content === 'string'
              ? currentStreamingMessageRef.current.content
              : ''
          );

          console.log('[useChatStream] ✅ Token appended, total length:',
            typeof currentStreamingMessageRef.current.content === 'string'
              ? currentStreamingMessageRef.current.content.length
              : 0
          );

          console.log('[useChatStream] ✅ Character streaming initiated for target length:', newTargetContent.length);
        }

        // Update current role
        if (role !== currentRole) {
          setCurrentRole(role);
          console.log('[useChatStream] Role changed from', currentRole, 'to', role);
        }
      },
      
      onLLMStreamEnd: (role: string) => {
        console.log('[useChatStream] 🏁 LLM STREAM ENDED for role:', role);

        if (currentStreamingMessageRef.current) {
          // Xử lý khác nhau cho role 'worker' và 'supervisor'
          if (role === 'worker') {
            console.log('[useChatStream] Worker stream ended - will collapse message after 3 seconds');

            // Đầu tiên, set status COMPLETED nhưng chưa collapse
            const completedMessage: ChatMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED,
              metadata: {
                ...currentStreamingMessageRef.current.metadata,
                processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime()
              }
            };

            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? completedMessage
                : msg
            ));

            // Sau 3 giây, mới collapse message để ẩn
            setTimeout(() => {
              console.log('[useChatStream] Worker message delay completed - collapsing now');
              const collapsedMessage: ChatMessage = {
                ...completedMessage,
                metadata: {
                  ...completedMessage.metadata,
                  collapsed: true, // Đánh dấu để UI hiển thị dạng collapsed
                  hideAfterDelay: true // Flag để biết đây là hide sau delay
                }
              };

              setMessages(prev => prev.map(msg =>
                msg.id === completedMessage.id
                  ? collapsedMessage
                  : msg
              ));
            }, 3000); // 3 giây delay
          } else if (role === 'supervisor') {
            console.log('[useChatStream] Supervisor stream ended - keeping message visible');
            // Supervisor message hiển thị bình thường
            const finalMessage: ChatMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED,
              timestamp: new Date(),
              metadata: {
                ...currentStreamingMessageRef.current.metadata,
                processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime()
              }
            };

            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? finalMessage
                : msg
            ));
          }
        }
      },

      onMessageCreated: (messageId: string, threadId: string, role: string, contentPreview: string) => {
        console.log('[useChatStream] 📝 Message created:', { messageId, threadId, role, contentPreview });

        // Chỉ gán messageId cho message của supervisor
        if (role === 'assistant' && currentStreamingMessageRef.current) {
          console.log('[useChatStream] Assigning messageId to supervisor message:', messageId);

          const updatedMessage: ChatMessage = {
            ...currentStreamingMessageRef.current,
            messageId: messageId, // Gán messageId từ API
            metadata: {
              ...currentStreamingMessageRef.current.metadata,
              apiMessageId: messageId,
              contentPreview: contentPreview
            }
          };

          setMessages(prev => prev.map(msg =>
            msg.id === currentStreamingMessageRef.current?.id
              ? updatedMessage
              : msg
          ));

          currentStreamingMessageRef.current = updatedMessage;
          console.log('[useChatStream] MessageId assigned successfully:', messageId);
        }
      },

      onStreamSessionEnd: (reason: string) => {
        console.log('[useChatStream] 🔚 STREAM SESSION ENDED:', reason);
        // Kết thúc toàn bộ session SSE
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText('');
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;

        // Clear stream error khi session kết thúc thành công
        setStreamError(null);
      },

      onStreamEnd: (threadId: string, runId: string) => {
        console.log('[useChatStream] 🏁 STREAM ENDED (fallback):', { threadId, runId });
        // Fallback cho trường hợp không có stream_session_end
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText('');
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;
      },

      onStreamError: (errorMessage: string, errorDetails?: unknown) => {
        console.error('[useChatStream] 🚨 STREAM ERROR CALLBACK TRIGGERED!');
        console.error('[useChatStream] 🚨 Error message:', errorMessage);
        console.error('[useChatStream] 🚨 Error details:', errorDetails);
        console.error('[useChatStream] 🚨 Retry content available:', lastMessageContentRef.current);

        // Set stream error state với retry content
        const errorState = {
          message: errorMessage,
          details: errorDetails,
          retryContent: lastMessageContentRef.current
        };

        console.error('[useChatStream] 🚨 Setting stream error state:', errorState);
        setStreamError(errorState);

        // Reset streaming state
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;

        console.log('[useChatStream] 🚨 Stream error handled, UI should show retry button');
      },

      onError: (error: Error) => {
        console.error('[useChatStream] 🚨 SSE ERROR:', error);

        // Xử lý timeout error đặc biệt - chỉ hiển thị nếu thực sự timeout
        if (error.message.includes('timeout')) {
          console.warn('[useChatStream] ⏰ SSE timeout detected');

          // Kiểm tra xem có đang streaming không - nếu có thì có thể là false positive
          if (isStreaming || isLoading || isThinking) {
            console.warn('[useChatStream] ⚠️ Timeout detected but still streaming - ignoring timeout error');
            return; // Không set error nếu đang streaming
          }

          setError('Kết nối bị timeout sau 1 phút không có phản hồi. Vui lòng thử lại.');
        } else {
          setError(error.message);
        }

        setIsStreaming(false);
        setIsLoading(false); // Tắt loading khi có lỗi SSE
        setIsThinking(false);
        setIsConnected(false);
      },
      
      onClose: () => {
        console.log('[useChatStream] SSE Closed');
        setIsConnected(false);
      }
    });

    return () => {
      // Cleanup
      // sseServiceRef.current?.disconnect();
    };
  }, [config, currentRole, isLoading, isStreaming, threadId]);

  /**
   * Load messages history từ API
   *
   * IMPORTANT: API trả về messages với sortDirection: 'DESC' (mới nhất trước)
   * Function này sẽ reverse thứ tự để có chronological order (cũ nhất trước)
   */
  const loadMessagesHistory = useCallback(async (
    page: number,
    isLoadMore: boolean = false
  ): Promise<void> => {
    if (!threadId || !apiServiceRef.current) {
      return;
    }

    // Tránh concurrent calls
    if (isLoadingHistory || isLoadingMoreHistory || isHistoryLoadingRef.current) {
      console.log('[useChatStream] History already loading, skipping...');
      return;
    }

    isHistoryLoadingRef.current = true;

    try {
      if (isLoadMore) {
        setIsLoadingMoreHistory(true);
      } else {
        setIsLoadingHistory(true);
      }
      setHistoryError(null);

      const query: GetMessagesQuery = {
        page,
        limit: config.messageHistory?.pageSize || 20,
        sortBy: 'createdAt',
        sortDirection: 'DESC', // Mới nhất trước
      };

      // Sử dụng singleton để tránh duplicate calls
      const response: GetMessagesResponseData = await chatApiSingleton.executeGetMessages(
        threadId,
        async () => {
          return apiServiceRef.current!.getMessages(threadId, query);
        }
      );

      // Convert HistoryMessage thành ChatMessage
      const convertedMessages = convertHistoryMessagesToChatMessages(response.items);

      // API trả về DESC (mới nhất trước), cần reverse để có chronological order (cũ nhất trước)
      const newChatMessages = convertedMessages.reverse();

      if (isLoadMore) {
        // Load more history = load tin nhắn cũ hơn
        // Append vào đầu danh sách (vì đây là tin nhắn cũ hơn)
        setHistoryMessages(prev => {
          const updated = [...newChatMessages, ...prev];
          console.log('[useChatStream] Updated history messages (load more):', {
            newCount: newChatMessages.length,
            prevCount: prev.length,
            totalCount: updated.length,
            firstNew: newChatMessages[0]?.timestamp,
            lastPrev: prev[prev.length - 1]?.timestamp
          });
          return updated;
        });
      } else {
        // Replace toàn bộ danh sách với thứ tự chronological
        console.log('[useChatStream] Setting initial history messages:', {
          count: newChatMessages.length,
          firstMessage: newChatMessages[0]?.timestamp,
          lastMessage: newChatMessages[newChatMessages.length - 1]?.timestamp
        });
        setHistoryMessages(newChatMessages);
      }

      // Update pagination state
      setCurrentHistoryPage(response.meta.currentPage);
      setTotalHistoryItems(response.meta.totalItems);
      setHasMoreHistory(response.meta.currentPage < response.meta.totalPages);

      if (config.debug) {
        console.log('[useChatStream] History messages loaded:', {
          threadId,
          page,
          itemCount: response.items.length,
          totalItems: response.meta.totalItems,
          hasMore: response.meta.currentPage < response.meta.totalPages,
        });
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load history messages:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load history messages';
      setHistoryError(errorMessage);
    } finally {
      setIsLoadingHistory(false);
      setIsLoadingMoreHistory(false);
      isHistoryLoadingRef.current = false;
    }
  }, [threadId, config.messageHistory?.pageSize, config.debug, isLoadingHistory, isLoadingMoreHistory]);

  /**
   * Load more history messages (next page)
   */
  const loadMoreHistory = useCallback(async (): Promise<void> => {
    if (!hasMoreHistory || isLoadingMoreHistory || isLoadingHistory) {
      return;
    }

    await loadMessagesHistory(currentHistoryPage + 1, true);
  }, [hasMoreHistory, isLoadingMoreHistory, isLoadingHistory, currentHistoryPage, loadMessagesHistory]);

  /**
   * Refresh history messages (reload từ đầu)
   */
  const refreshHistory = useCallback(async (): Promise<void> => {
    if (isLoadingHistory || isLoadingMoreHistory) {
      return;
    }

    setCurrentHistoryPage(1);
    await loadMessagesHistory(1, false);
  }, [isLoadingHistory, isLoadingMoreHistory, loadMessagesHistory]);

  /**
   * Reset history state
   */
  const resetHistory = useCallback(() => {
    setHistoryMessages([]);
    setIsLoadingHistory(false);
    setIsLoadingMoreHistory(false);
    setHasMoreHistory(true);
    setHistoryError(null);
    setCurrentHistoryPage(1);
    setTotalHistoryItems(0);
    currentThreadIdRef.current = null;
  }, []);

  /**
   * Tạo thread mới
   */
  const createNewThread = useCallback(async (name?: string): Promise<{ threadId: string; threadName: string }> => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      setIsCreatingThread(true);
      setError(null);

      console.log('[useChatStream] Creating new thread, cancelling current operations...');

      // 1. Cancel current SSE and API calls if any
      if (sseServiceRef.current && isConnected) {
        console.log('[useChatStream] Disconnecting current SSE connection...');
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop current streaming/loading if any
      if (isStreaming || isLoading) {
        console.log('[useChatStream] Stopping current streaming/loading...');
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current state
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Create new thread
      console.log('[useChatStream] Creating new thread via API...');
      const response = await apiServiceRef.current.createThread(name);

      // 5. Set new thread state
      setThreadId(response.threadId);
      setThreadName(response.name);

      console.log('[useChatStream] Thread created successfully:', response);

      // 6. Emit thread created event
      config.threadEvents?.onThreadCreated?.(response.threadId, response.name);

      return {
        threadId: response.threadId,
        threadName: response.name
      };
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to create thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create thread';
      setError(errorMessage);
      throw error;
    } finally {
      setIsCreatingThread(false);
    }
  }, [config.threadEvents, isConnected, isStreaming, isLoading, currentRunId]);

  /**
   * Load thread mới nhất (theo createdAt DESC)
   */
  const loadLatestThread = useCallback(async () => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      return;
    }

    try {
      setIsLoadingThreads(true);
      setError(null);

      // Sử dụng singleton để tránh duplicate calls
      const response = await chatApiSingleton.executeGetThreads(async () => {
        return apiServiceRef.current!.getThreads({
          page: 1,
          limit: 1,
          sortBy: 'createdAt',
          sortDirection: 'DESC'
        });
      });

      if (response.items.length > 0) {
        const latestThread = response.items[0];
        setThreadId(latestThread!.threadId);
        setThreadName(latestThread!.name);

        console.log('[useChatStream] Loaded latest thread:', latestThread);
      } else {
        // Không có thread nào, để null - sẽ tạo khi cần
        console.log('[useChatStream] No threads found, will create when needed');
        setThreadId(null);
        setThreadName(null);
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load latest thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load latest thread';
      setError(errorMessage);

      // Không tạo thread mới ở đây, để user tự tạo khi cần
      setThreadId(null);
      setThreadName(null);
    } finally {
      setIsLoadingThreads(false);
    }
  }, []); // Remove createNewThread dependency to avoid multiple calls

  // Auto-load latest thread khi khởi tạo - chỉ chạy một lần
  useEffect(() => {
    // Kiểm tra singleton để tránh multiple initialization
    if (chatApiSingleton.isInitialized()) {
      console.log('[useChatStream] Already initialized via singleton, skipping...');
      return;
    }

    // Thêm timeout để tránh React StrictMode double execution
    const timeoutId = setTimeout(() => {
      if (!hasLoadedInitialThreadRef.current && apiServiceRef.current && !isLoadingThreads && !isCreatingThread) {
        console.log('[useChatStream] Auto-loading latest thread...');
        chatApiSingleton.setInitialized();
        hasLoadedInitialThreadRef.current = true;
        loadLatestThread();
      }
    }, 100); // Tăng delay lên 100ms

    return () => clearTimeout(timeoutId);
  }, [loadLatestThread, isLoadingThreads, isCreatingThread]);

  // Auto load history khi threadId thay đổi
  useEffect(() => {
    // Clear previous timeout
    if (historyTimeoutRef.current) {
      clearTimeout(historyTimeoutRef.current);
    }

    // Kiểm tra xem threadId có thay đổi thực sự không
    if (currentThreadIdRef.current === threadId) {
      console.log('[useChatStream] ThreadId unchanged, skipping history load...');
      return;
    }

    currentThreadIdRef.current = threadId;

    if (threadId && (config.messageHistory?.autoLoad ?? true)) {
      console.log('[useChatStream] Scheduling auto-load history for thread:', threadId);

      // Debounce API call
      historyTimeoutRef.current = setTimeout(() => {
        console.log('[useChatStream] Executing auto-load history for thread:', threadId);
        loadMessagesHistory(1, false);
      }, 100); // 100ms debounce
    } else if (!threadId) {
      console.log('[useChatStream] No threadId, resetting history...');
      resetHistory();
    }

    // Cleanup timeout on unmount
    return () => {
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
      }
    };
  }, [threadId, config.messageHistory?.autoLoad, loadMessagesHistory, resetHistory]);

  /**
   * Gửi tin nhắn theo luồng mới
   */
  const sendMessage = useCallback(async (content: string) => {
    if (!apiServiceRef.current || !sseServiceRef.current) {
      setError('Services not initialized');
      return;
    }

    // Hủy streaming/loading hiện tại nếu có
    if (isStreaming || isLoading) {
      console.log('[useChatStream] Cancelling current operation before sending new message');
      try {
        if (stopStreamingRef.current) {
          await stopStreamingRef.current();
        }
      } catch (error) {
        console.warn('[useChatStream] Failed to stop current operation:', error);
      }
    }

    // Tạo thread nếu chưa có
    if (!threadId) {
      console.log('[useChatStream] No thread available, creating new thread...');
      try {
        await createNewThread();
        // Sau khi tạo xong, threadId sẽ được set, nhưng do async nên cần return và để user gửi lại
        return;
      } catch (error) {
        console.error('[useChatStream] Failed to create thread for message:', error);
        setError('Failed to create thread. Please try again.');
        return;
      }
    }

    try {
      setError(null);
      setStreamError(null); // Clear previous stream error
      setIsLoading(true); // Bắt đầu loading

      // Lưu content để có thể retry
      lastMessageContentRef.current = content;

      // 1. Hiển thị tin nhắn user
      const userMessage: ChatMessage = {
        id: uuidv4(),
        content,
        sender: 'user',
        timestamp: new Date(),
        status: MessageStatus.SENT,
        threadId: threadId!
      };

      setMessages(prev => [...prev, userMessage]);

      // 2. Chuẩn bị cho streaming (message sẽ được tạo khi nhận token đầu tiên)
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');

      // Reset token tracking
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // Reset streaming state
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // 4. Call API gửi tin nhắn
      const response: SendMessageResponse = await apiServiceRef.current.sendMessage(
        content,
        threadId!,
        true
      );

      console.log('[useChatStream] Message sent, response:', response);

      // Lưu runId từ response
      const newRunId = response.runId;
      setCurrentRunId(newRunId);

      console.log('runID', newRunId);
      

      // 5. Connect SSE để nhận stream
      console.log('[useChatStream] Connecting SSE...', { threadId: threadId!, runId: newRunId });
      await sseServiceRef.current.connect(threadId!, newRunId);
      console.log('[useChatStream] SSE connected successfully');

      // Loading sẽ được tắt khi stream kết thúc trong onStreamEnd

    } catch (error: unknown) {
      console.error('[useChatStream] Failed to send message:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      setError(errorMessage);
      setIsStreaming(false);
      setIsLoading(false); // Tắt loading khi có lỗi
      setIsThinking(false);

      // Remove the failed AI message
      if (currentStreamingMessageRef.current) {
        setMessages(prev => prev.filter(msg => msg.id !== currentStreamingMessageRef.current?.id));
        currentStreamingMessageRef.current = null;
      }
    }
  }, [threadId, createNewThread, isLoading, isStreaming]);

  /**
   * Dừng streaming hiện tại
   */
  const stopStreaming = useCallback(async () => {
    if (!currentRunId || !apiServiceRef.current) {
      return;
    }

    try {
      console.log('[useChatStream] Stopping current run:', currentRunId);
      
      // Call API để dừng run
      await apiServiceRef.current.stopRun(currentRunId);
      
      // Disconnect SSE
      sseServiceRef.current?.disconnect();
      
      // Reset state
      setIsStreaming(false);
      setIsLoading(false); // Tắt loading khi stop streaming
      setIsThinking(false);
      setCurrentRunId(null);
      setIsConnected(false);
      
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to stop streaming:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop streaming';
      setError(errorMessage);
    }
  }, [currentRunId]);

  /**
   * Send retry message without creating new user message
   */
  const sendRetryMessage = useCallback(async (content: string) => {
    if (!threadId) {
      console.error('[useChatStream] No thread ID for retry');
      return;
    }

    try {
      setError(null);
      setStreamError(null); // Clear previous stream error
      setIsLoading(true); // Bắt đầu loading

      console.log('[useChatStream] 🔄 RETRYING message without creating new user message:', content);

      // 1. Chuẩn bị cho streaming (message sẽ được tạo khi nhận token đầu tiên)
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');

      // Reset streaming state
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // 2. Call API gửi tin nhắn (KHÔNG tạo user message trong UI)
      const response: SendMessageResponse = await apiServiceRef.current!.sendMessage(content, threadId);

      console.log('[useChatStream] 🔄 Retry API response:', response);

      // 3. Lưu runId và bắt đầu streaming
      setCurrentRunId(response.runId);
      setIsLoading(false); // Tắt loading, bắt đầu streaming
      setIsStreaming(true);

      // 4. Connect SSE để nhận streaming response
      console.log('[useChatStream] 🔄 Connecting SSE for retry...', { threadId, runId: response.runId });
      await sseServiceRef.current!.connect(threadId, response.runId);
      console.log('[useChatStream] 🔄 SSE connected successfully for retry');

      console.log('[useChatStream] 🔄 Retry initiated successfully');
    } catch (error) {
      console.error('[useChatStream] ❌ Retry failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to retry message');
      setIsLoading(false);
      setIsStreaming(false);
      setIsThinking(false);
    }
  }, [threadId]);

  /**
   * Retry last message khi có stream error
   */
  const retryLastMessage = useCallback(async () => {
    if (!streamError?.retryContent) {
      console.warn('[useChatStream] ⚠️ No retry content available');
      return;
    }

    console.log('[useChatStream] 🔄 RETRY BUTTON CLICKED - Starting retry process');
    console.log('[useChatStream] 🔄 Retry content:', streamError.retryContent);
    console.log('[useChatStream] 🔄 Current messages count:', messages.length);

    // Clear stream error và retry
    setStreamError(null);

    console.log('[useChatStream] 🔄 Calling sendRetryMessage (no new user message)');
    await sendRetryMessage(streamError.retryContent);
  }, [streamError, sendRetryMessage, messages.length]);

  /**
   * Load specific thread by ID
   */
  const loadSpecificThread = useCallback(async (targetThreadId: string) => {
    try {
      setIsLoadingThreads(true);
      setError(null);

      console.log('[useChatStream] Loading specific thread:', targetThreadId);

      // Check if thread is deleted before making API call
      if (isThreadDeleted(targetThreadId)) {
        console.warn('[useChatStream] Thread is marked as deleted, skipping load:', targetThreadId);
        throw new Error(`Thread ${targetThreadId} has been deleted`);
      }

      // Verify thread exists by getting its detail using ThreadsService
      try {
        console.log('[useChatStream] Loading specific thread:', targetThreadId);
        const threadDetail = await ThreadsService.getThreadDetail(targetThreadId);

        if (threadDetail) {
          // Clear current messages
          setMessages([]);
          setCurrentStreamingText('');
          currentStreamingMessageRef.current = null;
          setStreamError(null);

          // Set new thread
          setThreadId(targetThreadId);
          setThreadName(threadDetail.name);

          // Emit thread loaded event
          config.threadEvents?.onThreadLoaded?.(targetThreadId, threadDetail.name);

          console.log('[useChatStream] Loaded specific thread successfully:', threadDetail);
        } else {
          throw new Error('Thread not found');
        }
      } catch (apiError: unknown) {
        // Nếu thread không tồn tại (404), có thể đã bị xóa
        if (apiError instanceof Error && apiError.message.includes('404')) {
          console.warn('[useChatStream] Thread not found (possibly deleted):', targetThreadId);
          throw new Error(`Thread ${targetThreadId} not found (possibly deleted)`);
        }
        // Re-throw other errors
        throw apiError;
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load specific thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load thread';
      setError(errorMessage);
    } finally {
      setIsLoadingThreads(false);
    }
  }, [config.threadEvents]);

  /**
   * Get current thread ID
   */
  const getCurrentThreadId = useCallback((): string | null => {
    return threadId;
  }, [threadId]);

  /**
   * Update thread name
   */
  const updateThreadName = useCallback(async (targetThreadId: string, newName: string) => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      console.log('[useChatStream] Updating thread name:', { targetThreadId, newName });

      // Update via ThreadsService
      const response = await ThreadsService.updateThread(targetThreadId, { name: newName });

      // Update local state if this is current thread
      if (threadId === targetThreadId) {
        console.log('[useChatStream] Updating local thread name:', {
          oldName: threadName,
          newName: response.name,
          threadId,
          targetThreadId
        });
        setThreadName(response.name);
      } else {
        console.log('[useChatStream] Not updating local state - different thread:', {
          currentThreadId: threadId,
          targetThreadId
        });
      }

      // Emit thread name changed event
      config.threadEvents?.onThreadNameChanged?.(targetThreadId, response.name);

      console.log('[useChatStream] Thread name updated:', response);
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to update thread name:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update thread name';
      setError(errorMessage);
      throw error;
    }
  }, [threadId, threadName, config.threadEvents]);

  /**
   * Switch to a different thread (disconnect current SSE and load new thread)
   */
  const switchToThread = useCallback(async (newThreadId: string) => {
    try {
      console.log('[useChatStream] Switching to thread:', newThreadId);
      console.log('[useChatStream] Current thread:', threadId);

      // Nếu đã là thread hiện tại, không cần làm gì
      if (threadId === newThreadId) {
        console.log('[useChatStream] Already on target thread, skipping switch');
        return;
      }

      const previousThreadId = threadId;

      // 1. Disconnect SSE hiện tại nếu có
      if (sseServiceRef.current && isConnected) {
        console.log('[useChatStream] Disconnecting current SSE connection...');
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop streaming hiện tại nếu có
      if (isStreaming || isLoading) {
        console.log('[useChatStream] Stopping current streaming/loading...');
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current messages và state
      console.log('[useChatStream] Clearing current messages and state...');
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      setError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Load new thread
      console.log('[useChatStream] Loading new thread:', newThreadId);
      try {
        await loadSpecificThread(newThreadId);

        // 5. Emit thread switched event
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, newThreadId);
        }

        console.log('[useChatStream] Successfully switched to thread:', newThreadId);
      } catch (loadError: unknown) {
        // Nếu không load được thread (có thể đã bị xóa), clear state
        console.error('[useChatStream] Failed to load target thread, clearing state:', loadError);
        setThreadId(null);
        setThreadName(null);

        // Emit error event
        const errorMessage = loadError instanceof Error ? loadError.message : 'Failed to load thread';
        setError(errorMessage);

        // Vẫn emit switched event để parent component biết thread switch failed
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, '');
        }

        throw loadError;
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to switch thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch thread';
      setError(errorMessage);
    }
  }, [threadId, isConnected, isStreaming, isLoading, currentRunId, loadSpecificThread, config.threadEvents]);

  /**
   * Clear tất cả messages và reset thread
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingText('');
    setThreadId(null);
    setThreadName(null);
    currentStreamingMessageRef.current = null;
    lastMessageContentRef.current = '';
    setStreamError(null);

    // Reset flag để có thể load thread mới
    hasLoadedInitialThreadRef.current = false;
    // Reset message history
    resetHistory();
  }, [resetHistory]);

  return {
    // State
    messages,
    isStreaming,
    isLoading,
    isThinking,
    currentStreamingText,
    currentRunId,
    threadId,
    threadName,
    isCreatingThread,
    isLoadingThreads,

    // Message History
    historyMessages,
    isLoadingHistory,
    isLoadingMoreHistory,
    hasMoreHistory,
    historyError,
    totalHistoryItems,

    // Actions
    sendMessage,
    stopStreaming,
    clearMessages,
    createNewThread,
    loadLatestThread,
    loadSpecificThread,
    switchToThread,
    getCurrentThreadId,
    updateThreadName,
    retryLastMessage,

    // Message History Actions
    loadMoreHistory,
    refreshHistory,

    // Status
    isConnected,
    error,
    streamError
  };
}

