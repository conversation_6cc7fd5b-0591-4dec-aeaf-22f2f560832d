/**
 * Test utility để verify SSE events mới hoạt động đúng
 */

import { ChatSSEService } from '@/shared/services/chat-sse.service';

/**
 * Mock SSE events data để test
 */
export const mockSSEEvents = {
  connected: {
    type: 'connected',
    data: JSON.stringify({
      threadId: "96bba755-082b-4d44-96d0-5e70af949c88",
      from: "live",
      timestamp: 1749617326387
    })
  },

  toolCallStart: {
    type: 'message',
    data: JSON.stringify({
      event: "tool_call_start",
      data: { role: "supervisor" },
      timestamp: 1749617293326
    })
  },

  toolCallEnd: {
    type: 'message',
    data: JSON.stringify({
      event: "tool_call_end",
      data: { role: "supervisor" },
      timestamp: 1749617293340
    })
  },

  streamTextToken: {
    type: 'message',
    data: JSON.stringify({
      event: "stream_text_token",
      data: { role: "worker", text: "I currently" },
      timestamp: 1749617294712
    })
  },

  llmStreamEndWorker: {
    type: 'message',
    data: JSON.stringify({
      event: "llm_stream_end",
      data: { role: "worker" },
      timestamp: 1749617294750
    })
  },

  llmStreamEndSupervisor: {
    type: 'message',
    data: JSON.stringify({
      event: "llm_stream_end",
      data: { role: "supervisor" },
      timestamp: 1749617296919
    })
  },

  messageCreated: {
    type: 'message',
    data: JSON.stringify({
      event: "message_created",
      data: {
        message_id: "a60dd1e5-df21-4940-b119-1e78e16660bb",
        thread_id: "96bba755-082b-4d44-96d0-5e70af949c88",
        role: "assistant",
        content_preview: "The task was successfully completed by the worker. I will now provide the response to you.\n\nFor the ",
        has_token_data: false,
        partial: false,
        timestamp: 1749617296905
      },
      timestamp: 1749617296905
    })
  },

  streamSessionEnd: {
    type: 'message',
    data: JSON.stringify({
      event: "stream_session_end",
      data: { reason: "Processing complete" },
      timestamp: 1749617296952
    })
  }
};

/**
 * Test SSE service với mock events
 */
export class SSEEventsTestRunner {
  private sseService: ChatSSEService;
  private testResults: { [key: string]: boolean } = {};
  private callbacks: any = {};

  constructor() {
    this.sseService = new ChatSSEService('http://localhost:3000');
    this.setupTestCallbacks();
  }

  private setupTestCallbacks() {
    this.callbacks = {
      onConnected: (data: unknown) => {
        console.log('✅ Connected event received:', data);
        this.testResults.connected = true;
      },

      onToolCallStart: (role: string) => {
        console.log('✅ Tool call start event received:', role);
        this.testResults.toolCallStart = role === 'supervisor';
      },

      onToolCallEnd: (role: string) => {
        console.log('✅ Tool call end event received:', role);
        this.testResults.toolCallEnd = role === 'supervisor';
      },

      onTextToken: (text: string, role: string) => {
        console.log('✅ Text token event received:', { text, role });
        this.testResults.textToken = text === 'I currently' && role === 'worker';
      },

      onLLMStreamEnd: (role: string) => {
        console.log('✅ LLM stream end event received:', role);
        if (role === 'worker') {
          this.testResults.llmStreamEndWorker = true;
        } else if (role === 'supervisor') {
          this.testResults.llmStreamEndSupervisor = true;
        }
      },

      onMessageCreated: (messageId: string, threadId: string, role: string, contentPreview: string) => {
        console.log('✅ Message created event received:', { messageId, threadId, role, contentPreview });
        this.testResults.messageCreated = (
          messageId === 'a60dd1e5-df21-4940-b119-1e78e16660bb' &&
          threadId === '96bba755-082b-4d44-96d0-5e70af949c88' &&
          role === 'assistant' &&
          contentPreview.includes('The task was successfully completed')
        );
      },

      onStreamSessionEnd: (reason: string) => {
        console.log('✅ Stream session end event received:', reason);
        this.testResults.streamSessionEnd = reason === 'Processing complete';
      },

      onError: (error: Error) => {
        console.error('❌ SSE Error:', error);
        this.testResults.error = true;
      }
    };

    this.sseService.setCallbacks(this.callbacks);
  }

  /**
   * Simulate receiving SSE events
   */
  async runTests(): Promise<{ [key: string]: boolean }> {
    console.log('🧪 Starting SSE Events Test...');

    // Reset test results
    this.testResults = {};

    try {
      // Test sequence: connected → tool_call_start → tool_call_end → text_token → llm_stream_end (worker) → llm_stream_end (supervisor) → message_created → stream_session_end

      console.log('\n1. Testing connected event...');
      this.simulateEvent(mockSSEEvents.connected);
      await this.wait(100);

      console.log('\n2. Testing tool_call_start event...');
      this.simulateEvent(mockSSEEvents.toolCallStart);
      await this.wait(100);

      console.log('\n3. Testing tool_call_end event...');
      this.simulateEvent(mockSSEEvents.toolCallEnd);
      await this.wait(100);

      console.log('\n4. Testing stream_text_token event...');
      this.simulateEvent(mockSSEEvents.streamTextToken);
      await this.wait(100);

      console.log('\n5. Testing llm_stream_end (worker) event...');
      this.simulateEvent(mockSSEEvents.llmStreamEndWorker);
      await this.wait(100);

      console.log('\n6. Testing llm_stream_end (supervisor) event...');
      this.simulateEvent(mockSSEEvents.llmStreamEndSupervisor);
      await this.wait(100);

      console.log('\n7. Testing message_created event...');
      this.simulateEvent(mockSSEEvents.messageCreated);
      await this.wait(100);

      console.log('\n8. Testing stream_session_end event...');
      this.simulateEvent(mockSSEEvents.streamSessionEnd);
      await this.wait(100);

      console.log('\n🧪 Test completed!');
      this.printResults();

      return this.testResults;
    } catch (error) {
      console.error('❌ Test failed:', error);
      return { error: true };
    }
  }

  private simulateEvent(eventData: { type: string; data: string }) {
    // Create mock MessageEvent
    const mockEvent = {
      type: eventData.type,
      data: eventData.data,
      lastEventId: '',
      origin: 'http://localhost:3000',
      ports: [],
      source: null
    } as MessageEvent;

    // Call the private handleSSEEvent method via reflection
    // Note: This is for testing only, in real usage events come from EventSource
    try {
      // Access private method for testing
      const handleSSEEvent = (this.sseService as any).handleSSEEvent;
      if (handleSSEEvent) {
        handleSSEEvent.call(this.sseService, mockEvent);
      } else {
        console.warn('⚠️ handleSSEEvent method not accessible for testing');
      }
    } catch (error) {
      console.error('❌ Error simulating event:', error);
    }
  }

  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private printResults() {
    console.log('\n📊 Test Results:');
    console.log('================');
    
    const tests = [
      { name: 'Connected Event', key: 'connected' },
      { name: 'Tool Call Start', key: 'toolCallStart' },
      { name: 'Tool Call End', key: 'toolCallEnd' },
      { name: 'Text Token', key: 'textToken' },
      { name: 'LLM Stream End (Worker)', key: 'llmStreamEndWorker' },
      { name: 'LLM Stream End (Supervisor)', key: 'llmStreamEndSupervisor' },
      { name: 'Message Created', key: 'messageCreated' },
      { name: 'Stream Session End', key: 'streamSessionEnd' }
    ];

    let passedCount = 0;
    tests.forEach(test => {
      const passed = this.testResults[test.key] === true;
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test.name}`);
      if (passed) passedCount++;
    });

    console.log(`\n📈 Summary: ${passedCount}/${tests.length} tests passed`);
    
    if (this.testResults.error) {
      console.log('❌ Errors occurred during testing');
    }
  }
}

/**
 * Run SSE events test
 */
export async function runSSEEventsTest(): Promise<void> {
  const testRunner = new SSEEventsTestRunner();
  await testRunner.runTests();
}

/**
 * Verify StreamingController methods exist
 */
export function verifyStreamingControllerMethods(): boolean {
  try {
    const { StreamingController } = require('@/shared/services/streaming-controller.service');
    const controller = new StreamingController();
    
    console.log('🔍 Verifying StreamingController methods...');
    
    const requiredMethods = [
      'forceCompleteStreaming',
      'addToken',
      'startStreaming',
      'stopStreaming',
      'stopAllStreaming'
    ];
    
    const missingMethods: string[] = [];
    
    requiredMethods.forEach(method => {
      if (typeof controller[method] === 'function') {
        console.log(`✅ ${method} exists`);
      } else {
        console.log(`❌ ${method} missing`);
        missingMethods.push(method);
      }
    });
    
    controller.destroy(); // Cleanup
    
    if (missingMethods.length === 0) {
      console.log('✅ All required methods exist');
      return true;
    } else {
      console.log(`❌ Missing methods: ${missingMethods.join(', ')}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Error verifying StreamingController:', error);
    return false;
  }
}
